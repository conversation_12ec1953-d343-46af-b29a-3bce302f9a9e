from django.conf import settings
from django.contrib.admin.sites import AdminSite
from django.contrib import admin

from common.utils.admin import BaseTipoAdmin


class MartinusAdminSite(AdminSite):
    site_url = None
    login_template = 'admin/martinus_login.html'
    empty_value_display = ''
    site_header = settings.MARTINUS_SITE_HEADER
    site_title = settings.MARTINUS_SITE_HEADER
    index_title = settings.MARTINUS_SITE_HEADER


site = MartinusAdminSite(name='martinus')


class MartinusModelAdmin(admin.ModelAdmin):
    list_max_show_all = 10000
    list_per_page = 50
    view_on_site = False
    actions_on_top = False
    actions_on_bottom = True

    def save_model(self, request, obj, form, change):
        if hasattr(obj, 'area'):
            if not obj.area:
                if request.user.area_corrente:
                    obj.area = request.user.area_corrente
        return super(MartinusModelAdmin, self).save_model(request, obj, form, change)

    def get_queryset(self, request):
        qs = super(MartinusModelAdmin, self).get_queryset(request)
        if hasattr(self.model, 'area'):
            if request.user.area_corrente:
                elenco_aree_figlie = request.user.area_corrente.get_descendants(include_self=True)
                qs = qs.filter(area__in=elenco_aree_figlie)
        return qs


class MartinusLogEntryAdmin(admin.ModelAdmin):
    date_hierarchy = 'action_time'
    readonly_fields = (
        'action_time', 'user', 'content_type', 'object_id',
        'object_link', 'action_flag', 'change_message',
    )
    list_filter = ['user', 'content_type', 'action_flag']
    suit_list_filter_horizontal = (
        'user', 'content_type', 'action_flag'
    )
    search_fields = ['object_repr', 'change_message', 'object_id']
    list_display = [
        'action_time',
        'user',
        'content_type',
        'object_id',
        'object_link',
        'action_flag_',
        'change_message',
    ]

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return request.user.is_superuser and request.method != 'POST'

    def has_delete_permission(self, request, obj=None):
        return False

    def action_flag_(self, obj):
        flags = {
            1: 'Inserimento',
            2: 'Modifica',
            3: 'Eliminazione',
        }
        return flags[obj.action_flag]

    def object_link(self, obj):
        if obj.action_flag == DELETION:
            link = escape(obj.object_repr)
        else:
            ct = obj.content_type
            try:
                url_link = reverse('publius:%s_%s_change' % (ct.app_label, ct.model), args=[obj.object_id])
                link = format_html('<a href="%s">%s</a>' % (url_link, escape(obj.object_repr)))
            except:
                link = 'N/D'
        return link
    object_link.allow_tags = True
    object_link.admin_order_field = 'object_repr'
    object_link.short_description = u'object'


# ALLEGATI
from common.allegati.admin import DocumentoAllegatoAdmin
from common.allegati.models import DocumentoAllegato, Categoria

site.register(DocumentoAllegato, DocumentoAllegatoAdmin)
site.register(Categoria, BaseTipoAdmin)

# ANAGRAFICA
from martinus.benefattori.admin import BenefattoreAdmin
from matthaeus.anagrafica.models import Benefattore, Tipologia, TipoComunicazione

site.register(Benefattore, BenefattoreAdmin)
site.register(Tipologia, BaseTipoAdmin)
site.register(TipoComunicazione, BaseTipoAdmin)

# DESTINAZIONI
from martinus.destinazioni.admin import ProgettoAdmin, SoggettoAdottabileAdmin
from martinus.destinazioni.admin import (
    TipologiaContributoOccasionaleAdmin, TipologiaContributoPeriodicoAdmin,
    ReferenteAdmin, AggiornamentoProgettoAdmin, AggiornamentoSoggettoAdottabileAdmin
)
from martinus.destinazioni.models import (
    Progetto, SoggettoAdottabile, TipologiaContributoOccasionale, 
    TipologiaContributoPeriodico, Referente,
    AggiornamentoProgetto, AggiornamentoSoggettoAdottabile
)

site.register(Progetto, ProgettoAdmin)
site.register(AggiornamentoProgetto, AggiornamentoProgettoAdmin)
site.register(AggiornamentoSoggettoAdottabile, AggiornamentoSoggettoAdottabileAdmin)
site.register(Referente, ReferenteAdmin)
site.register(SoggettoAdottabile, SoggettoAdottabileAdmin)
site.register(TipologiaContributoOccasionale, TipologiaContributoOccasionaleAdmin)
site.register(TipologiaContributoPeriodico, TipologiaContributoPeriodicoAdmin)

# ADESIONI
from martinus.adesioni.models import Adesione, SostegnoProgetto, AdozioneADistanza
from martinus.adesioni.models import ContributoOccasionale, ContributoPeriodico, SchemaRateazione
from martinus.adesioni.admin import AdesioneAdmin, SostegnoProgettoAdmin, AdozioneADistanzaAdmin
from martinus.adesioni.admin import ContributoOccasionaleAdmin, ContributoPeriodicoAdmin, SchemaRateazioneAdmin

site.register(Adesione, AdesioneAdmin)
site.register(SostegnoProgetto, SostegnoProgettoAdmin)
site.register(AdozioneADistanza, AdozioneADistanzaAdmin)
site.register(ContributoOccasionale, ContributoOccasionaleAdmin)
site.register(ContributoPeriodico, ContributoPeriodicoAdmin)
site.register(SchemaRateazione, SchemaRateazioneAdmin)

# DONAZIONI
from martinus.donazioni.models import Donazione, ModalitaPagamento, Causale
from martinus.donazioni.admin import DonazioneAdmin, ModalitaPagamentoAdmin, CausaleAdmin

site.register(Donazione, DonazioneAdmin)
site.register(ModalitaPagamento, ModalitaPagamentoAdmin)
site.register(Causale, CausaleAdmin)

# ANAGRAFICHE BASE
from common.anagraficabase.admin import TipoAreaAdmin, AreaAdmin, StatoAdmin, ProvinciaAdmin, LogImportazioneAdmin
from common.anagraficabase.models import TipoArea, Area, Provincia, Stato, Provincia, LogImportazione

site.register(TipoArea, TipoAreaAdmin)
site.register(Area, AreaAdmin)
site.register(Stato, StatoAdmin)
site.register(Provincia, ProvinciaAdmin)
site.register(LogImportazione, LogImportazioneAdmin)

# SCADENZIARIO
from common.scadenziario.admin import TipoScadenzaAdmin, ScadenzaFiscaleAdmin
from common.scadenziario.models import TipoScadenza, Scadenza

site.register(TipoScadenza, TipoScadenzaAdmin)
site.register(Scadenza, ScadenzaFiscaleAdmin)

# UTENTI
from common.authentication.admin import UtenteMagisterAdmin, ImpostazioniUtenteAdmin
from common.authentication.models import ImpostazioniUtente, UtenteMagister
from django.contrib.auth.admin import GroupAdmin
from django.contrib.auth.models import Group

site.register(UtenteMagister, UtenteMagisterAdmin)
site.register(ImpostazioniUtente, ImpostazioniUtenteAdmin)
site.register(Group, GroupAdmin)
