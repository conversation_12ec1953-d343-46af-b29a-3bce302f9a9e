from os.path import exists
from django.template import Context
from django_mptt_admin.admin import DjangoMpttAdmin

from django.contrib import admin
from django.contrib.admin import helpers
from django.utils.translation import ugettext_lazy as _
from django.urls import reverse
from django.conf.urls import url
from django.conf import settings
from django.shortcuts import get_object_or_404
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.core.exceptions import ValidationError
from django.template.defaultfilters import slugify
from django.utils import timezone
from django.contrib.admin import SimpleListFilter
from django.views.decorators.csrf import csrf_protect
from django.shortcuts import redirect
from django.utils.decorators import method_decorator

from import_export.admin import ImportMixin
from webodt.shortcuts import render_to_response as webodt_render_to_response
from modeltranslation.admin import TabbedDjangoJqueryTranslationAdmin as TabbedTranslationAdmin

from preferences.admin import PreferencesAdmin

from common.anagraficabase.models import (
    PianoDeiConti, PianoDeiContiImputabile, PianoDeiContiCosti, PianoDeiContiRicavi,
    PianoDeiContiLiquidita, PianoDeiContiPatrimoniale, PianoDeiContiAttivo, CentroDiCosto,
    Cambio, Esercizio, ContoFornitori, ContoClienti, ContoClientiFornitori
)
from common.anagraficabase.admin import EsercizioAdmin
from common.anagraficabase.utils import operazioni_chiusura_esercizio, get_dati_nuovo_conto
from common.anagraficabase.models import LIVELLO_PIANO, LIVELLO_PIANO_2, LIVELLO_PIANO_3

from matthaeus.admin import site
from matthaeus.anagrafica import forms
from common.stampe.utils import get_template_stampa
from matthaeus.admin import MatthaeusModelAdmin

from common.anagraficabase.admin import ChiusuraAreaInline
from common.anagraficabase.models import AbilitazionePianoDeiConti, Area, ChiusuraArea, AbilitazioneCentroDiCosto
from matthaeus.anagrafica.models import Anagrafica, Cliente, Fornitore, Banca, AbilitazioneAnagrafica, Tipologia, TipoComunicazione
from matthaeus.anagrafica.resources import PianoDeiContiResource, AnagraficaResource, AbilitazionePianoDeiContiResource

csrf_protect_m = method_decorator(csrf_protect)


class ImpostazioniMatthaeusAdmin(PreferencesAdmin):
    fieldsets = (
        (
            _('IMPOSTAZIONI STAMPE'), dict(
                fields=(
                    'intestazione_stampe',
                )
            )
        ),
        (
            _('IMPOSTAZIONI PIANO DEI CONTI'), dict(
                fields=(
                    'numero_cifre_mastro',
                    'numero_cifre_conto',
                    'numero_cifre_sottoconto',
                    'separatore_codice_conti',
                )
            ),
        ),
        (
            _('IMPOSTAZIONI BILANCIO'), dict(
                fields=(
                    'gestione_bilancio',
                    'escludi_chiusure_bilancio',
                )
            )
        ),
        (
            _('IMPOSTAZIONI PARTITA SEMPLICE/AVANZATA'), dict(
                fields=(
                    'gestione_controvalore_partita_semplice',
                    'gestione_controvalore_partita_avanzata',
                )
            )
        ),
        (
            _('IMPOSTAZIONI PARTITA DOPPIA'), dict(
                fields=(
                    'abilita_gestione_anagrafiche',
                    'abilita_controllo_inserimento_anagrafiche',
                )
            )
        ),
        (
            _('IMPOSTAZIONI PARTITARI'), dict(
                fields=(
                    'visualizza_saldo_fornitori',
                )
            )
        ),
    )

    @csrf_protect_m
    def changelist_view(self, request, extra_context=None):
        """
        If we only have a single preference object redirect to it,
        otherwise display listing.
        """
        model = self.model
        if model.objects.all().count() > 1:
            return super(PreferencesAdmin, self).changelist_view(request)
        else:
            obj = model.singleton.get()
            return redirect(
                reverse(
                    'matthaeus:%s_%s_change' % (
                        model._meta.app_label, model._meta.model_name
                    ),
                    args=(obj.id,)
                )
            )


class AbilitazionePianoDeiContiInline(admin.TabularInline):
    list_display = (
        'piano_dei_conti', 'area_abilitata',
    )
    model = AbilitazionePianoDeiConti
    autocomplete_fields = ('piano_dei_conti', 'area_abilitata',)


class AbilitazionePianoDeiContiAdmin(ImportMixin, MatthaeusModelAdmin):
    list_display = (
        'piano_dei_conti', 'area_abilitata',
    )
    list_filter = (
        ('area_abilitata', admin.RelatedOnlyFieldListFilter),
        'piano_dei_conti__livello', 'piano_dei_conti__tipologia'
    )
    search_fields = (
        'area_abilitata__nome', 'piano_dei_conti__descrizione', 'piano_dei_conti__codice'
    )
    resource_class = AbilitazionePianoDeiContiResource
    autocomplete_fields = ('piano_dei_conti', 'area_abilitata',)


site.register(AbilitazionePianoDeiConti, AbilitazionePianoDeiContiAdmin)


class LivelloContiFilter(SimpleListFilter):
    title = _('livello')
    parameter_name = 'livello'

    def lookups(self, request, model_admin):
        if settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == 2:
            return LIVELLO_PIANO_2
        elif settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == 3:
            return LIVELLO_PIANO_3
        else:
            return LIVELLO_PIANO

    def queryset(self, request, queryset):
        if self.value() is not None:
            valore = self.value()
            return queryset.filter(livello=valore)
        else:
            return queryset


class PianoDeiContiAdmin(ImportMixin, DjangoMpttAdmin):
    list_display = (
        'codice', 'descrizione', 'tipologia', 'conto_imputabile', 'livello',
        'valuta_default', 'impostazione_conto'
    )
    search_fields = ('descrizione', 'codice', )
    list_filter = (
        LivelloContiFilter,
        'valuta_default', 'tipologia', 'conto_imputabile', 'impostazione_conto',
        'gestione',
        'gestione_anagrafica',
    )
    list_max_show_all = 1600
    form = forms.PianoDeiContiForm
    title = _('Piano dei Conti')
    action_form = forms.AbilitazionePianoDeiContiActionForm
    autocomplete_fields = ('parent', )
    readonly_fields = ('codice', )
    resource_class = PianoDeiContiResource
    change_list_template = 'piano_change_list.html'
    # change_tree_template = 'piano_change_tree.html'
    inlines = [AbilitazionePianoDeiContiInline, ]
    blocco_livelli = ('codice_gruppo', 'codice_mastro', 'codice_conto', 'codice_sottoconto')
    fieldsets = (
        (
            _('Dati Generali'), dict(
                fields=[
                    'codice',
                    blocco_livelli,
                    'descrizione',
                    'tipologia',
                    'conto_imputabile',
                    'parent',
                    'livello',
                    'impostazione_conto',
                    'gestione',
                    'gestione_parziali_area',
                    'gestione_anagrafica',
                ]
            )
        ),
        (
            _('VALUTA'), dict(
                fields=(
                    ('conto_multivaluta', 'valuta_default'),
                )
            )
        ),
        (
            _('PREVENTIVO'), dict(
                fields=(
                    ('preventivo_dare', 'preventivo_avere'),
                )
            )
        ),
    )
    actions = ['stampa_piano_dei_conti', 'abilita_area']

    def get_actions(self, request):
        if request.user:
            if request.user.is_superuser:
                self.actions.append('imposta_natura_liquidita')
                self.actions.append('imposta_natura_patrimoniale')
                self.actions.append('imposta_natura_economico')
                self.actions.append('imposta_natura_costi')
                self.actions.append('imposta_natura_ricavi')
                self.actions.append('imposta_natura_ordine')
        actions = super(PianoDeiContiAdmin, self).get_actions(request)
        return actions

    def get_readonly_fields(self, request, obj=None):
        blocco_livelli = ('codice_gruppo', 'codice_mastro', 'codice_conto', 'codice_sottoconto')
        if settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == 2:
            blocco_livelli = ('codice_conto', 'codice_sottoconto')
        elif settings.NUMERO_LIVELLI_PIANO_DEI_CONTI == 3:
            blocco_livelli = ('codice_mastro', 'codice_conto', 'codice_sottoconto')
        self.fieldsets[0][1]['fields'][1] = blocco_livelli
        return super(PianoDeiContiAdmin, self).get_readonly_fields(request, obj)

    def is_drag_and_drop_enabled(self):
        return False

    def abilita_area(self, request, queryset):
        if 'area' in request.POST:
            elenco_area = request.POST.getlist('area')
            if elenco_area:
                if elenco_area[0]:
                    area = Area.objects.get(id=elenco_area[0])
                else:
                    area = Area.objects.get(id=elenco_area[1])
                if area:
                    for piano in queryset:
                        nuova_abilitazione, created = AbilitazionePianoDeiConti.objects.get_or_create(area_abilitata=area, piano_dei_conti=piano)
                        if created:
                            messages.success(request, 'Abilitato %s per area %s' % (piano, area))
                        else:
                            messages.warning(request, 'Abilitazione %s per area %s gia\' presente...' % (piano, area))
    abilita_area.short_description = 'Abilita Area selezionata:'

    def imposta_natura_liquidita(self, request, queryset):
        if queryset:
            for piano in queryset:
                piano.tipologia = 'liquidita'
                piano.save()
            messages.success(request, 'Assegnata tipologia LIQUIDITA per %s conti.' % queryset.count())
    imposta_natura_liquidita.short_description = 'Imposta natura: LIQUIDITA per i conti selezionati'

    def imposta_natura_patrimoniale(self, request, queryset):
        if queryset:
            for piano in queryset:
                piano.tipologia = 'patrimoniale'
                piano.save()
            messages.success(request, 'Assegnata tipologia PATRIMONIALE per %s conti.' % queryset.count())
    imposta_natura_patrimoniale.short_description = 'Imposta natura: PATRIMONIALE per i conti selezionati'

    def imposta_natura_economico(self, request, queryset):
        if queryset:
            for piano in queryset:
                piano.tipologia = 'economico'
                piano.save()
            messages.success(request, 'Assegnata tipologia ECONOMICO per %s conti.' % queryset.count())
    imposta_natura_economico.short_description = 'Imposta natura: ECONOMICO per i conti selezionati'

    def imposta_natura_costi(self, request, queryset):
        if queryset:
            for piano in queryset:
                piano.tipologia = 'costi'
                piano.save()
            messages.success(request, 'Assegnata tipologia COSTI per %s conti.' % queryset.count())
    imposta_natura_costi.short_description = 'Imposta natura: COSTI per i conti selezionati'

    def imposta_natura_ricavi(self, request, queryset):
        if queryset:
            for piano in queryset:
                piano.tipologia = 'ricavi'
                piano.save()
            messages.success(request, 'Assegnata tipologia RICAVI per %s conti.' % queryset.count())
    imposta_natura_ricavi.short_description = 'Imposta natura: RICAVI per i conti selezionati'

    def imposta_natura_ordine(self, request, queryset):
        if queryset:
            for piano in queryset:
                piano.tipologia = 'ordine'
                piano.save()
            messages.success(request, 'Assegnata tipologia D\'ORDINE per %s conti.' % queryset.count())
    imposta_natura_ordine.short_description = 'Imposta natura: D\'ORDINE per i conti selezionati'

    def stampa_piano_dei_conti(self, request, queryset):
        elenco_piani = queryset
        template = get_template_stampa('piano_dei_conti', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('piano_dei_conti')
        context = Context(
            dict(
                elenco_piani=elenco_piani,
                titolo=_('Piano dei Conti')
            )
        )
        filename = 'piano_dei_conti_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_piano_dei_conti.short_description = _('Stampa Piano dei conti (selezionati)')

    def get_queryset(self, request):
        queryset_base = super(PianoDeiContiAdmin, self).get_queryset(request)
        if request.user.area_corrente:
            if request.user.area_corrente.piano_dei_conti_personalizzato:
                queryset_base = queryset_base.filter(
                    id__in=AbilitazionePianoDeiConti.objects.filter(area_abilitata=request.user.area_corrente).values('piano_dei_conti__id')
                )
        return queryset_base

    def filter_tree_queryset(self, queryset, request):
        if request.user.area_corrente:
            if request.user.area_corrente.piano_dei_conti_personalizzato:
                queryset_base = queryset.filter(
                    id__in=AbilitazionePianoDeiConti.objects.filter(area_abilitata=request.user.area_corrente).values('piano_dei_conti__id')
                )
                return queryset_base
        return queryset

    def get_changeform_initial_data(self, request):
        initial_data = super(PianoDeiContiAdmin, self).get_changeform_initial_data(request=request)
        if 'insert_at' in request.GET:
            id_parente = request.GET.get('insert_at')
            dati_nuovo_conto = get_dati_nuovo_conto(id_parente)
            if dati_nuovo_conto['livello']:
                initial_data['livello'] = dati_nuovo_conto['livello']
                if dati_nuovo_conto['livello'] == 'sottoconto':
                    initial_data['conto_imputabile'] = True
            if dati_nuovo_conto['codice_gruppo']:
                initial_data['codice_gruppo'] = dati_nuovo_conto['codice_gruppo']
            if dati_nuovo_conto['codice_mastro']:
                initial_data['codice_mastro'] = dati_nuovo_conto['codice_mastro']
            if dati_nuovo_conto['codice_conto']:
                initial_data['codice_conto'] = dati_nuovo_conto['codice_conto']
            if dati_nuovo_conto['codice_sottoconto']:
                initial_data['codice_sottoconto'] = dati_nuovo_conto['codice_sottoconto']
            if dati_nuovo_conto['tipologia']:
                initial_data['tipologia'] = dati_nuovo_conto['tipologia']
            if dati_nuovo_conto['gestione']:
                initial_data['gestione'] = dati_nuovo_conto['gestione']
        return initial_data

    def get_insert_at_field(self):
        return "parent"


class PianoDeiContiTranslatedAdmin(PianoDeiContiAdmin, TabbedTranslationAdmin):
    pass


site.register(PianoDeiConti, PianoDeiContiTranslatedAdmin)


class AbilitazioneCentroDiCostoAdmin(MatthaeusModelAdmin):
    list_display = (
        'centro_di_costo', 'area_abilitata',
    )
    list_filter = (
        ('area_abilitata', admin.RelatedOnlyFieldListFilter),
        'centro_di_costo'
    )
    search_fields = (
        'area_abilitata__nome', 'centro_di_costo__descrizione', 'centro_di_costo__codice'
    )
    autocomplete_fields = ('centro_di_costo', 'area_abilitata',)


site.register(AbilitazioneCentroDiCosto, AbilitazioneCentroDiCostoAdmin)


class AbilitazioneCentriDiCostoInline(admin.TabularInline):
    list_display = (
        'centro_di_costo', 'area_abilitata',
    )
    model = AbilitazioneCentroDiCosto
    autocomplete_fields = ('centro_di_costo', 'area_abilitata',)


class CentroDiCostoAdmin(MatthaeusModelAdmin):
    search_fields = ('descrizione', 'nome', )

    def get_queryset(self, request):
        queryset_base = super(CentroDiCostoAdmin, self).get_queryset(request)
        if request.user.area_corrente:
            if request.user.area_corrente.centri_di_costo_personalizzati:
                queryset_base = queryset_base.filter(
                    id__in=AbilitazioneCentroDiCosto.objects.filter(area_abilitata=request.user.area_corrente).values('centro_di_costo__id')
                )
        return queryset_base

    def get_actions(self, request):
        if request.user.has_perm('anagraficabase.add_abilitazionecentrodicosto'):
            self.actions.append('abilita_centro_di_costo')
            self.action_form = forms.AbilitazioneCentroDiCostoActionForm
        else:
            if 'abilita_centro_di_costo' in self.actions:
                self.actions.remove('abilita_centro_di_costo')
            self.action_form = helpers.ActionForm
        return super(CentroDiCostoAdmin, self).get_actions(request)
        
    def abilita_centro_di_costo(self, request, queryset):
        if 'area' in request.POST:
            elenco_area = request.POST.getlist('area')
            if elenco_area:
                if elenco_area[0]:
                    area = Area.objects.get(id=elenco_area[0])
                else:
                    area = Area.objects.get(id=elenco_area[1])
                if area:
                    for centro_di_costo in queryset:
                        nuova_abilitazione, created = AbilitazioneCentroDiCosto.objects.get_or_create(area_abilitata=area, centro_di_costo=centro_di_costo)
                        if created:
                            messages.success(request, 'Abilitata %s per area %s' % (centro_di_costo, area))
                        else:
                            messages.warning(request, 'Abilitazione %s per area %s gia\' presente...' % (centro_di_costo, area))
    abilita_centro_di_costo.short_description = 'Abilita Area selezionata:'

    def save_model(self, request, obj, form, change):
        risultato = super(CentroDiCostoAdmin, self).save_model(request, obj, form, change)
        if request.user.area_corrente:
            area = request.user.area_corrente
            abilitazione, created = AbilitazioneCentroDiCosto.objects.get_or_create(area_abilitata=area, centro_di_costo=obj)
        return risultato


site.register(CentroDiCosto, CentroDiCostoAdmin)


class CambioAdmin(MatthaeusModelAdmin):
    list_display = (
        '__str__', 'valuta_origine', 'valuta_destinazione', 'data_inizio', 'data_fine'
    )
    list_filter = ('valuta_origine', 'valuta_destinazione', )
    date_hierarchy = ('data_inizio')


site.register(Cambio, CambioAdmin)


class AnagraficaAdmin(ImportMixin, MatthaeusModelAdmin):
    list_display = (
        'ragione_sociale', 'indirizzo', 'citta', 'telefono', 'fax', 'causale_contabile',
        'cliente', 'fornitore', 'attivo',
    )
    list_filter = (
        'attivo', 'cliente', 'fornitore',
        ('causale_contabile', admin.RelatedOnlyFieldListFilter),
        ('provincia', admin.RelatedOnlyFieldListFilter),
    )
    form = forms.AnagraficaForm
    resource_class = AnagraficaResource
    search_fields = (
        'id', 'ragione_sociale', 'citta', 'provincia__codice', 'cap',
        'telefono', 'fax', 'indirizzo', 'partita_iva', 'codice_fiscale',
    )
    readonly_fields = ('benefattore', )
    autocomplete_fields = ('causale_contabile', 'provincia', 'stato', 'tipologia', 'tipo_comunicazione')
    fieldsets = (
        (
            _('Dati Anagrafici'), dict(
                fields=(
                    'appellativo',
                    'ragione_sociale',
                    'indirizzo', 
                    'citta',
                    ('provincia', 'cap'),
                    ('stato', 'regione'),
                    'partita_iva',
                    'codice_fiscale',
                    'codice',
                    'codice_sdi',
                )
            )
        ),
        (
            _('Categorie'), dict(
                fields=(
                    'cliente',
                    'fornitore',
                    'banca',
                    'benefattore',
                    'attivo',
                    'tipologia',
                    'tipo_comunicazione',
                )
            )
        ),
        (
            _('Contatti'), dict(
                fields=(
                    'telefono',
                    'telefono_2',
                    'cellulare',
                    'fax',
                    'email',
                    'pec',
                )
            )
        ),
        (
            _('Causale contabile'), dict(
                fields=(
                    'causale_contabile',
                )
            )
        ),
        (
            _('Note'), dict(
                classes=('suit-tab suit-tab-dati_anagrafici',),
                fields=('note',),
            )
        ),
    )

    def get_actions(self, request):
        if request.user.has_perm('anagrafica.add_abilitazioneanagrafica'):
            self.actions.append('abilita_area')
            self.action_form = forms.AbilitazioneAnagraficaActionForm
        else:
            if 'abilita_area' in self.actions:
                self.actions.remove('abilita_area')
            self.action_form = helpers.ActionForm
        return super(AnagraficaAdmin, self).get_actions(request)

    def get_queryset(self, request):
        queryset_base = super(AnagraficaAdmin, self).get_queryset(request)
        if request.user.area_corrente:
            if request.user.area_corrente.anagrafica_personalizzata:
                queryset_base = queryset_base.filter(
                    id__in=AbilitazioneAnagrafica.objects.filter(area_abilitata=request.user.area_corrente).values('anagrafica__id')
                )
        return queryset_base

    def abilita_area(self, request, queryset):
        if 'area' in request.POST:
            elenco_area = request.POST.getlist('area')
            if elenco_area:
                if elenco_area[0]:
                    area = Area.objects.get(id=elenco_area[0])
                else:
                    area = Area.objects.get(id=elenco_area[1])
                if area:
                    for anagrafica in queryset:
                        nuova_abilitazione, created = AbilitazioneAnagrafica.objects.get_or_create(area_abilitata=area, anagrafica=anagrafica)
                        if created:
                            messages.success(request, 'Abilitata %s per area %s' % (anagrafica, area))
                        else:
                            messages.warning(request, 'Abilitazione %s per area %s gia\' presente...' % (anagrafica, area))
    abilita_area.short_description = 'Abilita Area selezionata:'

    def save_model(self, request, obj, form, change):
        risultato = super(AnagraficaAdmin, self).save_model(request, obj, form, change)
        if request.user.area_corrente:
            area = request.user.area_corrente
            abilitazione, created = AbilitazioneAnagrafica.objects.get_or_create(area_abilitata=area, anagrafica=obj)
        return risultato


site.register(Anagrafica, AnagraficaAdmin)


class ClienteAdmin(AnagraficaAdmin):
    list_display = (
        'ragione_sociale', 'indirizzo', 'citta', 'telefono', 'fax', 'causale_contabile',
        'attivo',
    )

    list_filter = (
        'attivo',
        ('causale_contabile', admin.RelatedOnlyFieldListFilter),
        ('provincia', admin.RelatedOnlyFieldListFilter),
    )


site.register(Cliente, ClienteAdmin)


class FornitoreAdmin(AnagraficaAdmin):
    list_display = (
        'ragione_sociale', 'indirizzo', 'citta', 'telefono', 'fax', 'causale_contabile',
        'attivo',
    )
    list_filter = (
        'attivo',
        ('causale_contabile', admin.RelatedOnlyFieldListFilter),
        ('provincia', admin.RelatedOnlyFieldListFilter),
    )


site.register(Fornitore, FornitoreAdmin)


class BancaAdmin(AnagraficaAdmin):
    list_display = (
        'ragione_sociale', 'indirizzo', 'citta', 'telefono', 'fax', 'causale_contabile',
        'attivo',
    )
    list_filter = (
        'attivo',
        ('causale_contabile', admin.RelatedOnlyFieldListFilter),
        ('provincia', admin.RelatedOnlyFieldListFilter),
    )


site.register(Banca, BancaAdmin)


class EsercizioMatthaeusAdmin(EsercizioAdmin):
    list_display = (
        'nome', 'descrizione', 'data_inizio', 'data_fine', 'get_link_esercizio_default', 'chiuso'
    )
    list_filter = ('chiuso', )
    autocomplete_fields = (
        'esercizio_successivo', 'conto_profitti_perdite',
        'conto_risultato_esercizio', 'conto_stato_patrimoniale_finale',
        'conto_stato_patrimoniale_iniziale', 'conto_risultati_esercizi_precedenti'
    )
    inlines = [ChiusuraAreaInline, ]
    suit_form_tabs = (
        ('generale', _('Generale')),
        ('chiusura', _('Chiusura Es.')),
        ('apertura', _('Apertura nuovo Es.')),
    )
    fieldsets = (
        (
            _('Dati Generali'), dict(
                classes=('suit-tab suit-tab-generale',),
                fields=(
                    'nome',
                    'descrizione',
                    'data_inizio',
                    'data_fine',
                    'chiuso',
                )
            )
        ),
        (
            _('Chiusura esercizio'), dict(
                classes=('suit-tab suit-tab-chiusura',),
                fields=(
                    'conto_profitti_perdite',
                    'conto_risultato_esercizio',
                    'conto_stato_patrimoniale_finale',
                    'data_operazione_chiusura',
                    'descrizione_operazione_chiusura',
                )
            )
        ),
        (
            _('Apertura nuovo esercizio'), dict(
                classes=('suit-tab suit-tab-apertura',),
                fields=(
                    'esercizio_successivo',
                    'conto_stato_patrimoniale_iniziale',
                    'conto_risultati_esercizi_precedenti',
                    'data_operazione_apertura',
                    'descrizione_operazione_apertura',
                ),
            )
        ),
    )

    def chiudi_esercizio_corrente(self, request, object_id):
        esercizio = get_object_or_404(Esercizio, pk=object_id)
        if esercizio:
            area_corrente = request.user.area_corrente
            try:
                elenco_messaggi = operazioni_chiusura_esercizio(esercizio, area_corrente)
                if elenco_messaggi:
                    for messaggio in elenco_messaggi:
                        messages.success(request, messaggio)
                messages.success(request, 'Esercizio chiuso correttamente!')
            except ValidationError as errore:
                messages.error(request, errore.message)
        else:
            messages.error(request, 'Errore nella chiusura dell\'esercizio!')
        url = reverse(
            'matthaeus:anagraficabase_esercizio_change', args=(object_id,)
        )
        return HttpResponseRedirect(url)
    
    def chiudi_area_corrente(self, request, object_id):
        esercizio = get_object_or_404(Esercizio, pk=object_id)
        if esercizio:
            area_corrente = request.user.area_corrente
            chiusura_area, created = ChiusuraArea.objects.get_or_create(area=area_corrente, esercizio=esercizio)
            chiusura_area.chiuso = True
            chiusura_area.data_chiusura = timezone.now()
            chiusura_area.save()
            messages.success(request, 'Area %s dell\'esercizio %s chiusa correttamente!' % (area_corrente, esercizio))
        else:
            messages.error(request, 'Errore nella chiusura dell\'esercizio!')
        url = reverse(
            'matthaeus:anagraficabase_esercizio_change', args=(object_id,)
        )
        return HttpResponseRedirect(url + '#chiusura')

    def apri_area_corrente(self, request, object_id):
        esercizio = get_object_or_404(Esercizio, pk=object_id)
        if esercizio:
            area_corrente = request.user.area_corrente
            try:
                chiusura_area = ChiusuraArea.objects.get(area=area_corrente, esercizio=esercizio)
                chiusura_area.delete()
                messages.success(request, 'Area %s dell\'esercizio %s riaperta.' % (area_corrente, esercizio))
            except ChiusuraArea.DoesNotExist:
                messages.error(request, 'area %s dell\'esercizio %s non presente!' % (area_corrente, esercizio))
        else:
            messages.error(request, 'Errore nella chiusura dell\'esercizio!')
        url = reverse(
            'matthaeus:anagraficabase_esercizio_change', args=(object_id,)
        )
        return HttpResponseRedirect(url+ '#chiusura')

    def get_urls(self):
        url_patterns = [
            url(
                r'^(\d+)/chiudieserciziocorrente/$',
                self.admin_site.admin_view(self.chiudi_esercizio_corrente),
                name='anagrafica_esercizio_chiudieserciziocorrente'
            ),
            url(
                r'^(\d+)/chiudiareacorrente/$',
                self.admin_site.admin_view(self.chiudi_area_corrente),
                name='anagrafica_esercizio_chiudiareacorrente'
            ),
            url(
                r'^(\d+)/apriareacorrente/$',
                self.admin_site.admin_view(self.apri_area_corrente),
                name='anagrafica_esercizio_apriareacorrente'
            ),
        ]
        url_patterns += super(EsercizioMatthaeusAdmin, self).get_urls()
        return url_patterns


site.register(Esercizio, EsercizioMatthaeusAdmin)


class PianoDeiContiCostiAdmin(PianoDeiContiAdmin):
    pass


site.register(PianoDeiContiCosti, PianoDeiContiCostiAdmin)


class PianoDeiContiRicaviAdmin(PianoDeiContiAdmin):
    pass


site.register(PianoDeiContiRicavi, PianoDeiContiRicaviAdmin)


class PianoDeiContiLiquiditaAdmin(PianoDeiContiAdmin):
    pass


site.register(PianoDeiContiLiquidita, PianoDeiContiLiquiditaAdmin)


class PianoDeiContiPatrimonialeAdmin(PianoDeiContiAdmin):
    pass


site.register(PianoDeiContiPatrimoniale, PianoDeiContiPatrimonialeAdmin)


class PianoDeiContiAttivoAdmin(PianoDeiContiAdmin):
    pass


site.register(PianoDeiContiAttivo, PianoDeiContiAttivoAdmin)


class PianoDeiContiImputabileAdmin(PianoDeiContiAdmin):
    pass


site.register(PianoDeiContiImputabile, PianoDeiContiImputabileAdmin)


class ContoFornitoriAdmin(PianoDeiContiAdmin):
    pass


site.register(ContoFornitori, ContoFornitoriAdmin)


class ContoClientiAdmin(PianoDeiContiAdmin):
    pass


site.register(ContoClienti, ContoClientiAdmin)


class ContoClientiFornitoriAdmin(PianoDeiContiAdmin):
    pass


site.register(ContoClientiFornitori, ContoClientiFornitoriAdmin)


class AbilitazioneAnagraficaAdmin(MatthaeusModelAdmin):
    list_display = (
        'anagrafica', 'area_abilitata',
    )
    list_filter = (
        ('area_abilitata', admin.RelatedOnlyFieldListFilter),
        'anagrafica__cliente', 'anagrafica__fornitore',
        'anagrafica__banca',
    )
    search_fields = (
        'area_abilitata__nome', 'anagrafica__ragione_sociale',
    )
    autocomplete_fields = ('area_abilitata', 'anagrafica')


site.register(AbilitazioneAnagrafica, AbilitazioneAnagraficaAdmin)


class TipologiaAdmin(MatthaeusModelAdmin):
    list_display = ('nome', 'descrizione')
    search_fields = ('nome', 'descrizione')


site.register(Tipologia, TipologiaAdmin)


class TipoComunicazioneAdmin(MatthaeusModelAdmin):
    list_display = ('nome', 'descrizione')
    search_fields = ('nome', 'descrizione')


site.register(TipoComunicazione, TipoComunicazioneAdmin)


