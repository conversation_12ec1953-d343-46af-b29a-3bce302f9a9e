from django.db import models
from django.utils.translation import ugettext_lazy as _
from django.urls import reverse

from common.anagraficabase.models import Provincia, Stato, Area
from matthaeus.movimenti.models import CausaleContabile

# aggiungi una classe Tipologia con il campo descrizione e aggiungi un campo tipologia alla classe Anagrafica
class Tipologia(models.Model):
    nome = models.CharField(_('nome'), max_length=200, unique=True)
    descrizione = models.CharField(_('descrizione'), max_length=200, null=True, blank=True)

    class Meta:
        verbose_name_plural = _('tipologie')
        verbose_name = _('tipologia')
        ordering = ('nome',)

    def __str__(self):
        return self.nome

    def get_url(self):
        url = reverse('matthaeus:anagrafica_tipologia_change', args=(self.id,))
        return url


class TipoComunicazione(models.Model):
    nome = models.Char<PERSON>ield(_('nome'), max_length=200, unique=True)
    descrizione = models.Char<PERSON>ield(_('descrizione'), max_length=200, null=True, blank=True)

    class Meta:
        verbose_name_plural = _('tipi comunicazione')
        verbose_name = _('tipo comunicazione')
        ordering = ('nome',)

    def __str__(self):
        return self.nome

    def get_url(self):
        url = reverse('matthaeus:anagrafica_tipocomunicazione_change', args=(self.id,))
        return url


class Anagrafica(models.Model):
    ragione_sociale = models.CharField(_('intestazione/ragione sociale'), max_length=200, unique=True)
    appellativo = models.CharField(_('appellativo'), max_length=200, null=True, blank=True)
    codice = models.CharField(_('codice'), max_length=200, null=True, blank=True)
    cliente = models.BooleanField(_('cliente'), default=False)
    fornitore = models.BooleanField(_('fornitore'), default=False)
    benefattore = models.BooleanField(_('benefattore'), default=False)
    banca = models.BooleanField(_('banca'), default=False)
    attivo = models.BooleanField(_('attivo'), default=True)
    presso = models.CharField(_('presso'), max_length=200, null=True, blank=True)
    indirizzo = models.CharField(_('indirizzo'), max_length=200, null=True, blank=True)
    citta = models.CharField(_('citta'), max_length=200, null=True, blank=True)
    regione = models.CharField(_('regione/stato'), max_length=200, null=True, blank=True)
    provincia = models.ForeignKey(
        Provincia, null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name=_('provincia'),
    )
    cap = models.CharField(_('cap'), max_length=50, null=True, blank=True)
    stato = models.ForeignKey(
        Stato, on_delete=models.SET_NULL, verbose_name=_('nazione'), null=True, blank=True,
    )
    telefono = models.CharField(_('telefono'), max_length=50, null=True, blank=True)
    telefono_2 = models.CharField(_('telefono 2'), max_length=50, null=True, blank=True)
    cellulare = models.CharField(_('cellulare'), max_length=50, null=True, blank=True)
    fax = models.CharField(_('fax'), max_length=50, null=True, blank=True)
    email = models.EmailField(_('email'), null=True, blank=True)
    pec = models.EmailField(_('pec'), null=True, blank=True)
    partita_iva = models.CharField(_('partita iva'), max_length=50, null=True, blank=True)
    codice_fiscale = models.CharField(_('codice fiscale'), max_length=50, null=True, blank=True)
    note = models.TextField(_('note'), blank=True)
    motivazioni = models.TextField(_('motivazioni'), blank=True)
    codice_sdi = models.CharField(
        _('Codice Destinatario SDI'), max_length=7, null=True, blank=True
    )
    causale_contabile = models.ForeignKey(
        CausaleContabile, null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name=_('causale contabile')
    )
    data_nascita = models.DateField(_('data di nascita'), null=True, blank=True)
    tipologia = models.ForeignKey(
        Tipologia, null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name=_('tipologia')
    )
    tipo_comunicazione = models.ForeignKey(
        TipoComunicazione, null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name=_('tipo comunicazione')
    )

    class Meta:
        verbose_name_plural = _('anagrafiche')
        verbose_name = _('anagrafica')
        ordering = ('ragione_sociale',)

    def __str__(self):
        nome = '%s' % self.ragione_sociale
        if not self.attivo:
            nome += ' (NON ATTIVO)'
        return nome

    def get_url(self):
        url = reverse('matthaeus:anagrafica_anagrafica_change', args=(self.id,))
        return url


class ClienteManager(models.Manager):

    def get_queryset(self):
        qs = super(ClienteManager, self).get_queryset()
        return qs.filter(cliente=True)


class Cliente(Anagrafica):
    objects = ClienteManager()

    class Meta:
        proxy = True
        verbose_name_plural = _('Clienti')
        verbose_name = _('Cliente')
    
    def save(self, *args, **kwargs):
        self.cliente = True
        return super(Cliente, self).save(*args, **kwargs)


class FornitoreManager(models.Manager):

    def get_queryset(self):
        qs = super(FornitoreManager, self).get_queryset()
        return qs.filter(fornitore=True)


class Fornitore(Anagrafica):
    objects = FornitoreManager()

    class Meta:
        proxy = True
        verbose_name_plural = _('Fornitori')
        verbose_name = _('Fornitore')

    def save(self, *args, **kwargs):
        self.fornitore = True
        return super(Fornitore, self).save(*args, **kwargs)


# aggiungi una classe proxy Benefattore con il campo benefattore impostato a True
class BenefattoreManager(models.Manager):

    def get_queryset(self):
        qs = super(BenefattoreManager, self).get_queryset()
        return qs.filter(benefattore=True)


class Benefattore(Anagrafica):
    objects = BenefattoreManager()

    class Meta:
        proxy = True
        verbose_name_plural = _('Benefattori')
        verbose_name = _('Benefattore')

    def save(self, *args, **kwargs):
        self.benefattore = True
        self.cliente = True
        return super(Benefattore, self).save(*args, **kwargs)

    def get_intestazione_stampe(self):
        intestazione = self.ragione_sociale
        if self.appellativo:
            intestazione = '%s %s' % (self.appellativo, intestazione)
        if self.presso:
            intestazione = '%s\npresso %s' % (intestazione, self.presso)
        if self.indirizzo:
            intestazione = '%s\n%s' % (intestazione, self.indirizzo)
        if self.cap:
            intestazione = '%s\n%s' % (intestazione, self.cap)
        if self.citta:
            intestazione = '%s %s' % (intestazione, self.citta)
        if self.provincia:
            intestazione = '%s (%s)' % (intestazione, self.provincia.codice)
        if self.regione:
            intestazione = '%s\n%s' % (intestazione, self.regione)
        if self.stato:
            intestazione = '%s\n%s' % (intestazione, self.stato)
        return intestazione


class BancaManager(models.Manager):

    def get_queryset(self):
        qs = super(BancaManager, self).get_queryset()
        return qs.filter(banca=True)


class Banca(Anagrafica):
    objects = BancaManager()

    class Meta:
        proxy = True
        verbose_name_plural = _('Banche')
        verbose_name = _('Banca')
    
    def save(self, *args, **kwargs):
        self.banca = True
        return super(Banca, self).save(*args, **kwargs)


class AbilitazioneAnagrafica(models.Model):
    anagrafica = models.ForeignKey(
        Anagrafica, on_delete=models.CASCADE, verbose_name=_('anagrafica')
    )
    area_abilitata = models.ForeignKey(
        Area, on_delete=models.CASCADE, verbose_name=_('area abilitata'), null=True, blank=True
    )

    class Meta:
        verbose_name_plural = _('abilitazioni anagrafiche')
        verbose_name = _('abilitazione anagrafica')
        ordering = ('anagrafica', 'area_abilitata')
        unique_together = ('anagrafica', 'area_abilitata')
        app_label = 'anagrafica'

    def __str__(self):
        return u'%s - %s' % (self.area_abilitata, self.anagrafica)


def verifica_anagrafica_abilitata(anagrafica, area):
    if area.anagrafica_personalizzata:
        try:
            AbilitazioneAnagrafica.objects.get(anagrafica=anagrafica, area_abilitata=area)
        except AbilitazioneAnagrafica.DoesNotExist:
            return False
    return True
